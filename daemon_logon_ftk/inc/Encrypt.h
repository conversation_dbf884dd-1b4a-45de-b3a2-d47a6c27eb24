#ifndef _ENCRYPT_H_
#define _ENCRYPT_H_
#include <stdlib.h>
#include <cstdio>
#include <cstring>
#include <iostream>
using namespace std;

#include <openssl/aes.h>
#include <iomanip>
#include <sstream>
#include <openssl/sha.h>

// OpenSSL 버전별 호환성 처리
#ifdef ROCKY_LINUX_9
    // Rocky Linux 9 (OpenSSL 3.x)
    #include <openssl/modes.h>
    #define OPENSSL_SUPPRESS_DEPRECATED
#elif defined(CENTOS_7X)
    // CentOS 7.x (OpenSSL 1.0.2) - modes.h 사용
    #include <openssl/modes.h>
#elif defined(CENTOS_6X)
    // CentOS 6.x (OpenSSL 1.0.1) - modes.h가 없을 수 있음
    // OpenSSL 1.0.1에서는 modes.h가 없거나 다른 위치에 있을 수 있음
#elif defined(OPENSSL_3X)
    #include <openssl/modes.h>
    #define OPENSSL_SUPPRESS_DEPRECATED
#elif defined(OPENSSL_11X)
    #include <openssl/modes.h>
#else
    // OpenSSL 1.0.x 또는 기타 버전
    // modes.h 포함을 하지 않을 수 있으므로 조건부 포함
#endif


#define KEY_SIZE 128
//#define BYTES_SIZE 5120
#define BYTES_SIZE 9216

struct ctr_state {
	unsigned char ivec[AES_BLOCK_SIZE];
	unsigned int num;
	unsigned char ecount[AES_BLOCK_SIZE];
};

class Encrypt
{
public:
	void set_key();
	void set_key(const char *key);
	void set_key_from_config(const char *config_key);
	void init_ctr(struct ctr_state *state, const unsigned char *iv);
	void encrypt(unsigned char *indata, unsigned char *outdata, int bytes_read);
	void decrypt(unsigned char *indata, unsigned char *outdata, int bytes_read);
	string sha256(const string& input);

	AES_KEY ase_key;
	unsigned char iv[16];
	unsigned char ckey[16];
};

// Common decryption utility functions
/**
 * Common function to decrypt AES Base64 encoded receiver number
 * @param encryptValue - encryption type value from packet
 * @param receiverValue - base64 encoded receiver number
 * @param decryptedReceiver - output buffer for decrypted receiver number
 * @param bufferSize - size of output buffer
 * @param configKey - encryption key from configuration
 * @return 0 on success, -1 on failure
 */
int decryptReceiverNumber(const char* encryptValue, const char* receiverValue,
                         char* decryptedReceiver, size_t bufferSize, const char* configKey);

/**
 * Common function to decrypt AES Base64 encoded receiver number with different key setting
 * Uses set_key() instead of set_key_from_config()
 * @param encryptValue - encryption type value from packet (mData.strEncoding)
 * @param receiverValue - base64 encoded receiver number
 * @param decryptedReceiver - output buffer for decrypted receiver number
 * @param bufferSize - size of output buffer
 * @return 0 on success, -1 on failure
 */
int decryptReceiverNumberWithDefaultKey(const char* encryptValue, const char* receiverValue,
                                       char* decryptedReceiver, size_t bufferSize);

#endif
